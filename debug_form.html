<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单调试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-400">表单调试页面</h1>
        
        <!-- 红包表单测试 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-orange-400">红包表单测试</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">参与方式</label>
                    <select id="test_redpack_participationType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="fanclub">粉丝团红包</option>
                        <option value="share">分享红包</option>
                        <option value="condition">条件型普通快币红包</option>
                        <option value="password">口令红包</option>
                    </select>
                </div>
                
                <!-- 附加条件字段 -->
                <div id="test_redpack_fanclubLevelField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">粉丝团红包等级</label>
                    <select id="test_redpack_fanclubLevel" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="粉丝团红包">粉丝团红包</option>
                        <option value="金粉团红包">金粉团红包</option>
                        <option value="钻粉团红包">钻粉团红包</option>
                        <option value="超粉团红包">超粉团红包</option>
                    </select>
                </div>
                <div id="test_redpack_amountField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">单个红包金额</label>
                    <input type="text" id="test_redpack_amount" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="如：6kb">
                </div>
                <div id="test_redpack_countField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">红包发放个数</label>
                    <input type="number" id="test_redpack_count" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="如：66">
                </div>
                <div class="md:col-span-2" id="test_redpack_passwordField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">口令红包内容</label>
                    <input type="text" id="test_redpack_password" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
            </div>
        </div>
        
        <!-- 幸运助手表单测试 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-green-400">幸运助手表单测试</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">参与条件</label>
                    <select id="test_lucky_condition" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="comment">评论</option>
                        <option value="like">点赞</option>
                        <option value="fanclub">粉丝团</option>
                        <option value="share">分享</option>
                        <option value="follow">关注主播</option>
                        <option value="watchtime">观看时长</option>
                        <option value="superfan">超粉团</option>
                    </select>
                </div>
                
                <!-- 附加条件字段 -->
                <div id="test_lucky_watchtimeField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">观看时长（分钟）</label>
                    <input type="number" id="test_lucky_watchtime" min="1" max="59"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="1-59分钟">
                </div>
                <div id="test_lucky_commentPasswordField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">评论口令</label>
                    <input type="text" id="test_lucky_commentPassword" maxlength="8"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="最多8个字符">
                </div>
                <div id="test_lucky_fanclubLevelField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">粉丝团等级</label>
                    <select id="test_lucky_fanclubLevel" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="LV1">LV1及以上</option>
                        <option value="LV2">LV2及以上</option>
                        <option value="LV3">LV3及以上</option>
                        <option value="LV4">LV4及以上</option>
                        <option value="LV5">LV5及以上</option>
                        <option value="LV6">LV6及以上</option>
                        <option value="LV7">LV7及以上</option>
                        <option value="LV8">LV8及以上</option>
                        <option value="LV9">LV9及以上</option>
                        <option value="LV10">LV10及以上</option>
                    </select>
                </div>
                <div id="test_lucky_superfanTypeField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">超粉团类型</label>
                    <select id="test_lucky_superfanType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="金粉">金粉</option>
                        <option value="钻粉">钻粉</option>
                        <option value="超粉">超粉</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <a href="index.html" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors inline-block">
                返回主页面
            </a>
        </div>
    </div>

    <script>
        // 红包表单事件监听器
        document.getElementById('test_redpack_participationType').addEventListener('change', function() {
            // 隐藏所有附加条件字段
            document.getElementById('test_redpack_fanclubLevelField').style.display = 'none';
            document.getElementById('test_redpack_amountField').style.display = 'none';
            document.getElementById('test_redpack_countField').style.display = 'none';
            document.getElementById('test_redpack_passwordField').style.display = 'none';
            
            // 根据选择显示对应的附加条件字段
            if (this.value === 'fanclub') {
                document.getElementById('test_redpack_fanclubLevelField').style.display = 'block';
                document.getElementById('test_redpack_amountField').style.display = 'block';
                document.getElementById('test_redpack_countField').style.display = 'block';
            } else if (this.value === 'share') {
                document.getElementById('test_redpack_amountField').style.display = 'block';
                document.getElementById('test_redpack_countField').style.display = 'block';
            } else if (this.value === 'password') {
                document.getElementById('test_redpack_passwordField').style.display = 'block';
                document.getElementById('test_redpack_amountField').style.display = 'block';
                document.getElementById('test_redpack_countField').style.display = 'block';
            }
        });

        // 幸运助手表单事件监听器
        document.getElementById('test_lucky_condition').addEventListener('change', function() {
            // 隐藏所有附加条件字段
            document.getElementById('test_lucky_watchtimeField').style.display = 'none';
            document.getElementById('test_lucky_commentPasswordField').style.display = 'none';
            document.getElementById('test_lucky_fanclubLevelField').style.display = 'none';
            document.getElementById('test_lucky_superfanTypeField').style.display = 'none';
            
            // 根据选择显示对应的附加条件字段
            if (this.value === 'watchtime') {
                document.getElementById('test_lucky_watchtimeField').style.display = 'block';
            } else if (this.value === 'comment') {
                document.getElementById('test_lucky_commentPasswordField').style.display = 'block';
            } else if (this.value === 'fanclub') {
                document.getElementById('test_lucky_fanclubLevelField').style.display = 'block';
            } else if (this.value === 'superfan') {
                document.getElementById('test_lucky_superfanTypeField').style.display = 'block';
            }
        });
    </script>
</body>
</html>
