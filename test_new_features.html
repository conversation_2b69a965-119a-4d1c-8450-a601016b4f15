<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-400">新功能测试页面</h1>
        
        <div class="space-y-8">
            <!-- 幸运助手附加条件测试 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-400">幸运助手附加条件测试</h2>
                
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-yellow-400">批量导入格式示例：</h3>
                    
                    <div class="space-y-2">
                        <div>
                            <p class="text-sm text-gray-300 mb-1">观看时长：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,观看时长,1min,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">评论：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,评论,快乐,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">粉丝团：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,粉丝团,LV3,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">超粉团：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,超粉团,金粉,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">其他条件（点赞）：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,点赞,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 红包附加条件测试 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-orange-400">红包附加条件测试</h2>
                
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-yellow-400">批量导入格式示例：</h3>
                    
                    <div class="space-y-2">
                        <div>
                            <p class="text-sm text-gray-300 mb-1">条件型普通快币红包：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,2025-08-30 18:00:00,3min,1/4,条件型普通快币红包,66
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">粉丝团红包：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,金粉团红包,6kb,66
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">分享红包：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,2025-08-30 18:00:00,3min,1/4,分享红包,6kb,66
                            </div>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-300 mb-1">口令红包：</p>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono">
                                Before Party,2025-08-30 18:00:00,3min,1/4,口令红包,快乐,6kb,66
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试说明 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-purple-400">测试说明</h2>
                
                <div class="space-y-3 text-sm text-gray-300">
                    <p><strong>1. 幸运助手新增附加条件：</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>观看时长：增加观看时长输入框（1-59分钟，需小于开奖倒计时）</li>
                        <li>评论：增加评论口令输入框（最长8个字符）</li>
                        <li>粉丝团：增加粉丝团等级下拉选单（LV1-LV10及以上）</li>
                        <li>超粉团：增加超粉团类型下拉选单（金粉、钻粉、超粉）</li>
                    </ul>
                    
                    <p><strong>2. 红包新增附加条件：</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>粉丝团红包：增加粉丝团红包等级 + 单个红包金额 + 红包发放个数</li>
                        <li>分享红包：增加单个红包金额 + 红包发放个数</li>
                        <li>口令红包：增加口令内容 + 单个红包金额 + 红包发放个数</li>
                        <li>条件型普通快币红包：保持原状</li>
                    </ul>
                    
                    <p><strong>3. 批量导入格式已更新：</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>支持新的附加条件字段</li>
                        <li>批量导入和批量修改窗口格式介绍已更新</li>
                        <li>详细信息展示区域会显示对应的附加条件</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <a href="index.html" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors inline-block">
                返回主页面测试
            </a>
        </div>
    </div>
</body>
</html>
