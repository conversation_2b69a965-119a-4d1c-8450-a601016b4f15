// 配置管理模块
class ConfigManager {
    constructor() {
        this.currentEditingProduct = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadRooms();
        this.loadProducts();
    }

    bindEvents() {
        // 添加直播间
        document.getElementById('addRoomBtn').addEventListener('click', () => {
            this.addRoom();
        });

        // 添加产品
        document.getElementById('addProductBtn').addEventListener('click', () => {
            this.showProductForm();
        });

        // 保存配置
        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.exportConfig();
        });

        // 加载本地配置
        document.getElementById('loadLocalConfigBtn').addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('loadConfigFile').click();
        });

        // 加载线上配置
        document.getElementById('loadOnlineConfigBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.showOnlineConfigModal();
        });

        document.getElementById('loadConfigFile').addEventListener('change', (e) => {
            this.importConfig(e.target.files[0]);
        });

        // 批量导入
        document.getElementById('batchImportBtn').addEventListener('click', () => {
            this.showBatchImportModal();
        });

        // 批量修改
        document.getElementById('batchEditBtn').addEventListener('click', () => {
            this.showBatchEditModal();
        });

        // 清空配置
        document.getElementById('clearConfigBtn').addEventListener('click', () => {
            this.showClearConfigModal();
        });

        // 产品类型选择
        document.getElementById('productType').addEventListener('change', () => {
            this.updateProductForm();
        });
    }

    // 加载直播间列表
    loadRooms() {
        const rooms = window.storageManager.getRooms();
        const roomsList = document.getElementById('roomsList');
        const selectedRoom = document.getElementById('selectedRoom');
        
        // 更新直播间列表
        roomsList.innerHTML = '';
        selectedRoom.innerHTML = '<option value="">选择直播间</option>';
        
        rooms.forEach(room => {
            // 添加到管理列表
            const roomDiv = document.createElement('div');
            roomDiv.className = 'flex items-center justify-between bg-gray-700 p-3 rounded';
            roomDiv.innerHTML = `
                <span class="font-medium text-lg">${room}</span>
                <button onclick="configManager.removeRoom('${room}')" 
                        class="text-red-400 hover:text-red-300 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            `;
            roomsList.appendChild(roomDiv);
            
            // 添加到选择下拉框
            const option = document.createElement('option');
            option.value = room;
            option.textContent = room;
            selectedRoom.appendChild(option);
        });
    }

    // 添加直播间
    addRoom() {
        const input = document.getElementById('newRoomName');
        const roomName = input.value.trim();
        
        if (!roomName) {
            this.showMessage('请输入直播间名称', 'error');
            return;
        }
        
        if (window.storageManager.addRoom(roomName)) {
            input.value = '';
            this.loadRooms();
            this.showMessage('直播间添加成功', 'success');
        } else {
            this.showMessage('直播间已存在或添加失败', 'error');
        }
    }

    // 删除直播间
    removeRoom(roomName) {
        if (confirm(`确定要删除直播间"${roomName}"吗？这将同时删除该直播间下的所有产品配置。`)) {
            if (window.storageManager.removeRoom(roomName)) {
                this.loadRooms();
                this.loadProducts();
                this.showMessage('直播间删除成功', 'success');
            } else {
                this.showMessage('删除失败', 'error');
            }
        }
    }

    // 显示产品配置表单
    showProductForm() {
        const selectedRoom = document.getElementById('selectedRoom').value;
        const productType = document.getElementById('productType').value;

        if (!selectedRoom) {
            this.showMessage('请先选择直播间', 'error');
            return;
        }

        if (!productType) {
            this.showMessage('请选择产品类型', 'error');
            return;
        }

        this.currentEditingProduct = null;
        this.updateProductForm();

        // 自动滚动到产品配置表单区域
        this.scrollToProductForm();
    }

    // 滚动到产品配置表单区域（居中显示）
    scrollToProductForm() {
        const productForm = document.getElementById('productForm');
        const header = document.getElementById('mainHeader');

        if (productForm && header) {
            // 检查头部是否处于冻结模式
            const isHeaderFrozen = window.app && window.app.headerFrozen;

            // 等待表单内容渲染完成后再滚动
            setTimeout(() => {
                if (isHeaderFrozen) {
                    // 冻结模式下，计算居中位置
                    const headerHeight = header.offsetHeight;
                    const viewportHeight = window.innerHeight;
                    const formHeight = productForm.offsetHeight;
                    const formTop = productForm.offsetTop;

                    // 计算让表单在可视区域居中的位置
                    const availableHeight = viewportHeight - headerHeight;
                    const centerOffset = (availableHeight - formHeight) / 2;
                    const targetPosition = formTop - headerHeight - Math.max(centerOffset, 20);

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                } else {
                    // 非冻结模式下，使用scrollIntoView居中显示
                    productForm.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }, 100); // 等待100ms让表单内容完全渲染
        }
    }

    // 滚动到配置列表区域
    scrollToConfigSection() {
        const configListSection = document.getElementById('configListSection');
        const header = document.getElementById('mainHeader');

        if (configListSection && header) {
            // 检查头部是否处于冻结模式
            const isHeaderFrozen = window.app && window.app.headerFrozen;

            if (isHeaderFrozen) {
                // 冻结模式下，考虑固定头部的实际高度
                const headerHeight = header.offsetHeight;
                const targetPosition = configListSection.offsetTop - headerHeight - 20; // 额外20px间距

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            } else {
                // 非冻结模式下，直接滚动到目标位置
                configListSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    }

    // 更新产品配置表单
    updateProductForm() {
        const productType = document.getElementById('productType').value;
        const productForm = document.getElementById('productForm');
        
        if (!productType) {
            productForm.classList.add('hidden');
            return;
        }
        
        let formHTML = '';
        
        if (productType === 'redpack') {
            formHTML = this.getRedpackForm();
        } else if (productType === 'lucky') {
            formHTML = this.getLuckyForm();
        }
        
        productForm.innerHTML = `
            <h3 class="text-lg font-semibold mb-4 text-yellow-400">
                ${productType === 'redpack' ? '红包配置' : '幸运助手配置'}
            </h3>
            ${formHTML}
            <div class="flex space-x-4 mt-6">
                <button onclick="configManager.saveProduct()"
                        class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                    保存配置
                </button>
                <button onclick="configManager.cancelProductForm()"
                        class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                    取消
                </button>
            </div>
        `;

        productForm.classList.remove('hidden');

        // 延迟绑定事件监听器，确保DOM元素已完全渲染
        setTimeout(() => {
            this.bindFormEventListeners(productType);
        }, 10);
    }

    // 绑定表单事件监听器
    bindFormEventListeners(productType) {
        if (productType === 'redpack') {
            const participationTypeSelect = document.getElementById('redpack_participationType');
            if (participationTypeSelect) {
                participationTypeSelect.addEventListener('change', function() {
                    // 隐藏所有附加条件字段
                    const fanclubLevelField = document.getElementById('redpack_fanclubLevelField');
                    const amountField = document.getElementById('redpack_amountField');
                    const countField = document.getElementById('redpack_countField');
                    const passwordField = document.getElementById('redpack_passwordField');

                    if (fanclubLevelField) fanclubLevelField.style.display = 'none';
                    if (amountField) amountField.style.display = 'none';
                    if (countField) countField.style.display = 'none';
                    if (passwordField) passwordField.style.display = 'none';

                    // 根据选择显示对应的附加条件字段
                    if (this.value === 'fanclub') {
                        if (fanclubLevelField) fanclubLevelField.style.display = 'block';
                        if (amountField) amountField.style.display = 'block';
                        if (countField) countField.style.display = 'block';
                    } else if (this.value === 'share') {
                        if (amountField) amountField.style.display = 'block';
                        if (countField) countField.style.display = 'block';
                    } else if (this.value === 'password') {
                        if (passwordField) passwordField.style.display = 'block';
                        if (amountField) amountField.style.display = 'block';
                        if (countField) countField.style.display = 'block';
                    }
                    // 条件型普通快币红包保持原有状态，不显示附加条件
                });
            }
        } else if (productType === 'lucky') {
            const conditionSelect = document.getElementById('lucky_condition');
            if (conditionSelect) {
                conditionSelect.addEventListener('change', function() {
                    // 隐藏所有附加条件字段
                    const watchtimeField = document.getElementById('lucky_watchtimeField');
                    const commentPasswordField = document.getElementById('lucky_commentPasswordField');
                    const fanclubLevelField = document.getElementById('lucky_fanclubLevelField');
                    const superfanTypeField = document.getElementById('lucky_superfanTypeField');

                    if (watchtimeField) watchtimeField.style.display = 'none';
                    if (commentPasswordField) commentPasswordField.style.display = 'none';
                    if (fanclubLevelField) fanclubLevelField.style.display = 'none';
                    if (superfanTypeField) superfanTypeField.style.display = 'none';

                    // 根据选择显示对应的附加条件字段
                    if (this.value === 'watchtime') {
                        if (watchtimeField) watchtimeField.style.display = 'block';
                    } else if (this.value === 'comment') {
                        if (commentPasswordField) commentPasswordField.style.display = 'block';
                    } else if (this.value === 'fanclub') {
                        if (fanclubLevelField) fanclubLevelField.style.display = 'block';
                    } else if (this.value === 'superfan') {
                        if (superfanTypeField) superfanTypeField.style.display = 'block';
                    }
                });
            }

            // 观看时长验证
            const watchtimeInput = document.getElementById('lucky_watchtime');
            const countdownInput = document.getElementById('lucky_countdown');
            if (watchtimeInput && countdownInput) {
                watchtimeInput.addEventListener('input', function() {
                    const countdown = parseInt(countdownInput.value);
                    const watchtime = parseInt(this.value);
                    if (watchtime && countdown && watchtime >= countdown) {
                        alert('观看时长必须小于开奖倒计时');
                        this.value = Math.max(1, countdown - 1);
                    }
                });

                // 开奖倒计时变化时重新验证观看时长
                countdownInput.addEventListener('change', function() {
                    const watchtime = parseInt(watchtimeInput.value);
                    const countdown = parseInt(this.value);
                    if (watchtime && countdown && watchtime >= countdown) {
                        alert('观看时长必须小于开奖倒计时，已自动调整');
                        watchtimeInput.value = Math.max(1, countdown - 1);
                    }
                });
            }
        }
    }

    // 获取红包配置表单
    getRedpackForm() {
        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">红包发送时间</label>
                    <input type="datetime-local" id="redpack_sendTime" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">开奖时间</label>
                    <select id="redpack_drawTime" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="3">3分钟</option>
                        <option value="10">10分钟</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">当前轮次</label>
                    <input type="number" id="redpack_currentRound" min="1" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">总轮次</label>
                    <input type="number" id="redpack_totalRounds" min="1" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">参与方式</label>
                    <select id="redpack_participationType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="fanclub">粉丝团红包</option>
                        <option value="share">分享红包</option>
                        <option value="condition">条件型普通快币红包</option>
                        <option value="password">口令红包</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">快币数</label>
                    <input type="number" id="redpack_coins" min="0" max="99999999" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <!-- 附加条件字段 -->
                <div id="redpack_fanclubLevelField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">粉丝团红包等级</label>
                    <select id="redpack_fanclubLevel" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="粉丝团红包">粉丝团红包</option>
                        <option value="金粉团红包">金粉团红包</option>
                        <option value="钻粉团红包">钻粉团红包</option>
                        <option value="超粉团红包">超粉团红包</option>
                    </select>
                </div>
                <div id="redpack_amountField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">单个红包金额</label>
                    <input type="text" id="redpack_amount"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="如：6kb">
                </div>
                <div id="redpack_countField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">红包发放个数</label>
                    <input type="number" id="redpack_count" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="如：66">
                </div>
                <div class="md:col-span-2" id="redpack_passwordField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">口令红包内容</label>
                    <input type="text" id="redpack_password"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
            </div>
        `;
    }

    // 获取幸运助手配置表单
    getLuckyForm() {
        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">参与条件</label>
                    <select id="lucky_condition" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="comment">评论</option>
                        <option value="like">点赞</option>
                        <option value="fanclub">粉丝团</option>
                        <option value="share">分享</option>
                        <option value="follow">关注主播</option>
                        <option value="watchtime">观看时长</option>
                        <option value="superfan">超粉团</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">中奖人数</label>
                    <input type="number" id="lucky_winners" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">当前轮次</label>
                    <input type="number" id="lucky_currentRound" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">总轮次</label>
                    <input type="number" id="lucky_totalRounds" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">开奖倒计时（分钟）</label>
                    <input type="number" id="lucky_countdown" min="1" max="60"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">定时发送时间</label>
                    <input type="datetime-local" id="lucky_scheduledTime"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium mb-2">奖品说明</label>
                    <textarea id="lucky_prizeDescription" rows="3"
                              class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"></textarea>
                </div>
                <!-- 附加条件字段 -->
                <div id="lucky_watchtimeField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">观看时长（分钟）</label>
                    <input type="number" id="lucky_watchtime" min="1" max="59"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="1-59分钟">
                </div>
                <div id="lucky_commentPasswordField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">评论口令</label>
                    <input type="text" id="lucky_commentPassword" maxlength="8"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="最多8个字符">
                </div>
                <div id="lucky_fanclubLevelField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">粉丝团等级</label>
                    <select id="lucky_fanclubLevel" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="LV1">LV1及以上</option>
                        <option value="LV2">LV2及以上</option>
                        <option value="LV3">LV3及以上</option>
                        <option value="LV4">LV4及以上</option>
                        <option value="LV5">LV5及以上</option>
                        <option value="LV6">LV6及以上</option>
                        <option value="LV7">LV7及以上</option>
                        <option value="LV8">LV8及以上</option>
                        <option value="LV9">LV9及以上</option>
                        <option value="LV10">LV10及以上</option>
                    </select>
                </div>
                <div id="lucky_superfanTypeField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">超粉团类型</label>
                    <select id="lucky_superfanType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="金粉">金粉</option>
                        <option value="钻粉">钻粉</option>
                        <option value="超粉">超粉</option>
                    </select>
                </div>
            </div>
        `;
    }

    // 保存产品配置
    saveProduct() {
        const selectedRoom = document.getElementById('selectedRoom').value;
        const productType = document.getElementById('productType').value;
        
        let productData = {
            room: selectedRoom,
            type: productType
        };
        
        if (productType === 'redpack') {
            productData = { ...productData, ...this.getRedpackData() };
        } else if (productType === 'lucky') {
            productData = { ...productData, ...this.getLuckyData() };
        }
        
        // 验证数据
        if (!this.validateProductData(productData)) {
            return;
        }
        
        if (this.currentEditingProduct) {
            // 更新现有产品
            if (window.storageManager.updateProduct(this.currentEditingProduct, productData)) {
                this.showMessage('产品配置更新成功', 'success');
            } else {
                this.showMessage('更新失败', 'error');
                return;
            }
        } else {
            // 添加新产品
            const productId = window.storageManager.addProduct(productData);
            if (productId) {
                this.showMessage('产品配置添加成功', 'success');
            } else {
                this.showMessage('添加失败', 'error');
                return;
            }
        }
        
        this.cancelProductForm();
        this.loadProducts();
        
        // 通知Dashboard更新
        if (window.dashboardManager) {
            window.dashboardManager.updateDashboard();
        }
    }

    // 获取红包数据
    getRedpackData() {
        return {
            sendTime: document.getElementById('redpack_sendTime').value,
            drawTime: parseInt(document.getElementById('redpack_drawTime').value),
            currentRound: parseInt(document.getElementById('redpack_currentRound').value),
            totalRounds: parseInt(document.getElementById('redpack_totalRounds').value),
            participationType: document.getElementById('redpack_participationType').value,
            coins: parseInt(document.getElementById('redpack_coins').value),
            password: document.getElementById('redpack_password')?.value || '',
            // 附加条件字段
            fanclubLevel: document.getElementById('redpack_fanclubLevel')?.value || '',
            amount: document.getElementById('redpack_amount')?.value || '',
            count: parseInt(document.getElementById('redpack_count')?.value || 0)
        };
    }

    // 获取幸运助手数据
    getLuckyData() {
        return {
            condition: document.getElementById('lucky_condition').value,
            winners: parseInt(document.getElementById('lucky_winners').value),
            currentRound: parseInt(document.getElementById('lucky_currentRound').value),
            totalRounds: parseInt(document.getElementById('lucky_totalRounds').value),
            countdown: parseInt(document.getElementById('lucky_countdown').value),
            scheduledTime: document.getElementById('lucky_scheduledTime').value,
            prizeDescription: document.getElementById('lucky_prizeDescription').value,
            // 附加条件字段
            watchtime: parseInt(document.getElementById('lucky_watchtime')?.value || 0),
            commentPassword: document.getElementById('lucky_commentPassword')?.value || '',
            fanclubLevel: document.getElementById('lucky_fanclubLevel')?.value || '',
            superfanType: document.getElementById('lucky_superfanType')?.value || ''
        };
    }

    // 验证产品数据
    validateProductData(data) {
        // 首先进行冲突校验
        if (!this.validateProductConflicts(data)) {
            return false;
        }

        if (data.type === 'redpack') {
            if (!data.sendTime) {
                this.showMessage('请设置红包发送时间', 'error');
                return false;
            }
            if (!data.currentRound || !data.totalRounds) {
                this.showMessage('请设置轮次信息', 'error');
                return false;
            }
            if (data.currentRound > data.totalRounds) {
                this.showMessage('当前轮次不能大于总轮次', 'error');
                return false;
            }
            if (!data.coins || data.coins < 0) {
                this.showMessage('请设置有效的快币数', 'error');
                return false;
            }
        } else if (data.type === 'lucky') {
            if (!data.scheduledTime) {
                this.showMessage('请设置定时发送时间', 'error');
                return false;
            }
            if (!data.winners || data.winners < 1) {
                this.showMessage('请设置有效的中奖人数', 'error');
                return false;
            }
            if (!data.currentRound || !data.totalRounds) {
                this.showMessage('请设置轮次信息', 'error');
                return false;
            }
            if (data.currentRound > data.totalRounds) {
                this.showMessage('当前轮次不能大于总轮次', 'error');
                return false;
            }
            if (!data.countdown || data.countdown < 1 || data.countdown > 60) {
                this.showMessage('开奖倒计时必须在1-60分钟之间', 'error');
                return false;
            }
            if (data.condition === 'watchtime' && data.countdown <= data.watchtime) {
                this.showMessage('观看时长类幸运星，开奖倒计时必须大于观看时长', 'error');
                return false;
            }

            // 检查时间间隔限制
            if (!this.validateLuckyTimeInterval(data.scheduledTime)) {
                this.showMessage('幸运星活动时间间隔必须大于1分钟', 'error');
                return false;
            }
        }
        
        return true;
    }

    // 验证产品配置冲突
    validateProductConflicts(data) {
        const products = window.storageManager.getProducts();
        const productTime = new Date(data.sendTime || data.scheduledTime);

        for (let product of products) {
            // 跳过当前编辑的产品
            if (product.id === this.currentEditingProduct) {
                continue;
            }

            // 检查同一直播间的冲突
            if (product.room === data.room) {
                // 检查相同轮次冲突
                if (product.currentRound === data.currentRound &&
                    product.totalRounds === data.totalRounds) {
                    this.showMessage(`直播间"${data.room}"已存在第${data.currentRound}/${data.totalRounds}轮配置，不可重复设置`, 'error');
                    return false;
                }

                // 检查相同时间冲突
                const existingTime = new Date(product.sendTime || product.scheduledTime);
                if (Math.abs(productTime - existingTime) < 60000) { // 1分钟内视为相同时间
                    const timeStr = productTime.toLocaleString('zh-CN');
                    this.showMessage(`直播间"${data.room}"在时间"${timeStr}"附近已有配置，不可重复设置`, 'error');
                    return false;
                }
            }
        }

        return true;
    }

    // 验证幸运星时间间隔
    validateLuckyTimeInterval(newTime) {
        const products = window.storageManager.getProducts();
        const newDateTime = new Date(newTime);

        for (let product of products) {
            if (product.type === 'lucky' && product.id !== this.currentEditingProduct) {
                const existingTime = new Date(product.scheduledTime);
                const timeDiff = Math.abs(newDateTime - existingTime);
                if (timeDiff < 60000) { // 小于1分钟
                    return false;
                }
            }
        }

        return true;
    }

    // 取消产品表单
    cancelProductForm() {
        document.getElementById('productForm').classList.add('hidden');
        document.getElementById('productType').value = '';
        this.currentEditingProduct = null;
    }

    // 加载产品配置列表
    loadProducts() {
        const products = window.storageManager.getProducts();
        const configList = document.getElementById('configList');
        
        if (products.length === 0) {
            configList.innerHTML = '<div class="text-gray-400 text-center py-8">暂无配置项</div>';
            return;
        }
        
        configList.innerHTML = products.map(product => {
            const timeStr = product.sendTime || product.scheduledTime;
            const time = timeStr ? new Date(timeStr).toLocaleString('zh-CN') : '未设置';
            
            let details = '';
            if (product.type === 'redpack') {
                details = `第${product.currentRound}/${product.totalRounds}轮 | ${this.getParticipationTypeText(product.participationType)}`;

                // 添加附加条件信息
                if (product.participationType === 'fanclub' && product.fanclubLevel) {
                    details += ` | ${product.fanclubLevel}`;
                }
                if (product.participationType === 'password' && product.password) {
                    details += ` | 口令：${product.password}`;
                }
                if ((product.participationType === 'fanclub' || product.participationType === 'share' || product.participationType === 'password') && product.amount && product.count) {
                    details += ` | ${product.amount} × ${product.count}个`;
                }
                if (product.participationType === 'condition' && product.coins) {
                    details += ` | ${product.coins}快币`;
                }

                details += ` | 开奖时间：${product.drawTime}分钟`;
            } else if (product.type === 'lucky') {
                const roundInfo = product.currentRound && product.totalRounds ?
                    `第${product.currentRound}/${product.totalRounds}轮 | ` : '';
                details = `${roundInfo}${product.winners}人中奖 | ${this.getConditionText(product.condition)}`;

                // 添加附加条件信息
                if (product.condition === 'watchtime' && product.watchtime) {
                    details += ` | 观看${product.watchtime}分钟`;
                } else if (product.condition === 'comment' && product.commentPassword) {
                    details += ` | 口令：${product.commentPassword}`;
                } else if (product.condition === 'fanclub' && product.fanclubLevel) {
                    details += ` | ${product.fanclubLevel}及以上`;
                } else if (product.condition === 'superfan' && product.superfanType) {
                    details += ` | ${product.superfanType}`;
                }

                details += ` | ${product.countdown}分钟倒计时`;
            }

            return `
                <div class="bg-gray-700 p-4 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-4 mb-2">
                                <span class="text-lg font-semibold text-blue-400">${product.room}</span>
                                <span class="px-2 py-1 rounded text-xs ${product.type === 'redpack' ? 'bg-orange-600' : 'bg-green-600'} text-white">
                                    ${product.type === 'redpack' ? '红包' : '幸运助手'}
                                </span>
                                <span class="text-yellow-400 font-mono">${time}</span>
                            </div>
                            <div class="text-sm text-gray-300">${details}</div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="configManager.copyProduct('${product.id}')"
                                    class="text-green-400 hover:text-green-300 transition-colors" title="复制配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                            <button onclick="configManager.editProduct('${product.id}')"
                                    class="text-blue-400 hover:text-blue-300 transition-colors" title="编辑配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button onclick="configManager.removeProduct('${product.id}')"
                                    class="text-red-400 hover:text-red-300 transition-colors" title="删除配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 编辑产品
    editProduct(productId) {
        const products = window.storageManager.getProducts();
        const product = products.find(p => p.id === productId);

        if (!product) return;

        this.currentEditingProduct = productId;
        this.fillProductForm(product);

        // 滚动到编辑表单
        this.scrollToProductForm();
    }

    // 复制产品
    copyProduct(productId) {
        const products = window.storageManager.getProducts();
        const product = products.find(p => p.id === productId);

        if (!product) return;

        this.currentEditingProduct = null; // 设为null表示新建
        this.fillProductForm(product, true); // true表示复制模式

        // 滚动到编辑表单
        this.scrollToProductForm();
    }

    // 填充产品表单
    fillProductForm(product, isCopy = false) {
        // 设置表单值
        document.getElementById('selectedRoom').value = product.room;
        document.getElementById('productType').value = product.type;

        this.updateProductForm();

        // 填充表单数据
        setTimeout(() => {
            if (product.type === 'redpack') {
                // 复制模式下清空时间，让用户重新设置
                document.getElementById('redpack_sendTime').value = isCopy ? '' : (product.sendTime || '');
                document.getElementById('redpack_drawTime').value = product.drawTime || '3';
                document.getElementById('redpack_currentRound').value = product.currentRound || '';
                document.getElementById('redpack_totalRounds').value = product.totalRounds || '';
                document.getElementById('redpack_participationType').value = product.participationType || 'fanclub';
                document.getElementById('redpack_coins').value = product.coins || '';

                // 填充附加条件字段
                if (document.getElementById('redpack_password')) {
                    document.getElementById('redpack_password').value = product.password || '';
                }
                if (document.getElementById('redpack_fanclubLevel')) {
                    document.getElementById('redpack_fanclubLevel').value = product.fanclubLevel || '粉丝团红包';
                }
                if (document.getElementById('redpack_amount')) {
                    document.getElementById('redpack_amount').value = product.amount || '';
                }
                if (document.getElementById('redpack_count')) {
                    document.getElementById('redpack_count').value = product.count || '';
                }

                // 触发参与方式变化事件以显示对应的附加条件字段
                document.getElementById('redpack_participationType').dispatchEvent(new Event('change'));

            } else if (product.type === 'lucky') {
                document.getElementById('lucky_condition').value = product.condition || 'comment';
                document.getElementById('lucky_winners').value = product.winners || '';
                document.getElementById('lucky_currentRound').value = product.currentRound || '';
                document.getElementById('lucky_totalRounds').value = product.totalRounds || '';
                document.getElementById('lucky_countdown').value = product.countdown || '';
                // 复制模式下清空时间，让用户重新设置
                document.getElementById('lucky_scheduledTime').value = isCopy ? '' : (product.scheduledTime || '');
                document.getElementById('lucky_prizeDescription').value = product.prizeDescription || '';

                // 填充附加条件字段
                if (document.getElementById('lucky_watchtime')) {
                    document.getElementById('lucky_watchtime').value = product.watchtime || '';
                }
                if (document.getElementById('lucky_commentPassword')) {
                    document.getElementById('lucky_commentPassword').value = product.commentPassword || '';
                }
                if (document.getElementById('lucky_fanclubLevel')) {
                    document.getElementById('lucky_fanclubLevel').value = product.fanclubLevel || 'LV1';
                }
                if (document.getElementById('lucky_superfanType')) {
                    document.getElementById('lucky_superfanType').value = product.superfanType || '金粉';
                }

                // 触发参与条件变化事件以显示对应的附加条件字段
                document.getElementById('lucky_condition').dispatchEvent(new Event('change'));
            }
        }, 100);

        document.getElementById('productForm').classList.remove('hidden');

        if (isCopy) {
            this.showMessage('配置已复制，请修改时间后保存', 'info');
        }
    }

    // 删除产品
    removeProduct(productId) {
        if (confirm('确定要删除这个产品配置吗？')) {
            if (window.storageManager.removeProduct(productId)) {
                this.loadProducts();
                this.showMessage('产品配置删除成功', 'success');
                
                // 通知Dashboard更新
                if (window.dashboardManager) {
                    window.dashboardManager.updateDashboard();
                }
            } else {
                this.showMessage('删除失败', 'error');
            }
        }
    }

    // 导出配置
    exportConfig() {
        window.storageManager.exportConfig();
        this.showMessage('配置导出成功', 'success');
    }

    // 显示线上配置加载模态框
    showOnlineConfigModal() {
        const modalContent = `
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-orange-400 mb-4">加载线上配置</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">配置文件URL：</label>
                        <input type="url" id="onlineConfigUrl"
                               placeholder="https://example.com/config.ksconf"
                               class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                    </div>
                    <div class="text-sm text-gray-400">
                        <p>💡 支持的URL类型：</p>
                        <ul class="list-disc list-inside mt-1 space-y-1">
                            <li>直接文件链接（如：https://example.com/config.ksconf）</li>
                            <li>GitHub Raw文件链接</li>
                            <li>其他可直接访问的配置文件链接</li>
                        </ul>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="window.app.hideModal()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="window.configManager.loadOnlineConfig()"
                            class="px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded transition-colors">
                        读取在线配置
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('加载线上配置', modalContent);
    }

    // 加载线上配置
    async loadOnlineConfig() {
        const urlInput = document.getElementById('onlineConfigUrl');
        const url = urlInput?.value?.trim();

        if (!url) {
            this.showMessage('请输入配置文件URL', 'error');
            return;
        }

        try {
            this.showMessage('正在加载线上配置...', 'info');

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const configText = await response.text();

            // 创建一个临时的File对象来复用现有的导入逻辑
            const blob = new Blob([configText], { type: 'text/plain' });
            const file = new File([blob], 'online-config.ksconf', { type: 'text/plain' });

            // 使用现有的导入配置方法
            await this.importConfigFromFile(file);

            window.app.hideModal();
            this.showMessage('线上配置加载成功', 'success');

        } catch (error) {
            console.error('加载线上配置失败:', error);
            this.showMessage(`加载失败: ${error.message}`, 'error');
        }
    }

    // 导入配置（从文件）
    importConfig(file) {
        if (!file) return;
        this.importConfigFromFile(file);
    }

    // 从文件导入配置的通用方法
    async importConfigFromFile(file) {
        try {
            await window.storageManager.importConfig(file);
            this.loadRooms();
            this.loadProducts();

            // 通知Dashboard更新
            if (window.dashboardManager) {
                window.dashboardManager.updateDashboard();
            }
        } catch (error) {
            throw new Error(error.message || '配置文件格式错误');
        }
    }

    // 获取参与方式文本
    getParticipationTypeText(type) {
        const types = {
            'fanclub': '粉丝团红包',
            'share': '分享红包',
            'condition': '条件型普通快币红包',
            'password': '口令红包'
        };
        return types[type] || type;
    }

    // 获取条件文本
    getConditionText(condition) {
        const conditions = {
            'comment': '评论',
            'like': '点赞',
            'fanclub': '粉丝团',
            'share': '分享',
            'follow': '关注主播',
            'watchtime': '观看时长',
            'superfan': '超粉团'
        };
        return conditions[condition] || condition;
    }

    // 显示批量导入模态框
    showBatchImportModal() {
        const rooms = window.storageManager.getRooms();
        const roomsText = rooms.join('、');

        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-blue-400 mb-2">批量导入产品配置</h4>
                    <p class="text-sm text-gray-300">请按照指定格式录入产品配置信息，每行一个产品配置。</p>
                    <div class="mt-2 p-2 bg-blue-900 rounded text-blue-200 text-sm">
                        <strong>📋 当前可用直播间：</strong>${roomsText}
                    </div>
                </div>

                <!-- 横向布局：左侧格式说明，右侧输入区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 左侧：格式说明 -->
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <div>
                            <h5 class="font-semibold text-orange-400 mb-2">红包格式：</h5>

                            <div class="space-y-2">
                                <div>
                                    <p class="text-xs text-gray-300 mb-1">条件型普通快币红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,条件型普通快币红包,快币数
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,条件型普通快币红包,66
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">粉丝团红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,粉丝团红包,粉丝团红包等级,单个红包金额,红包发放个数
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,金粉团红包,6kb,66
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">分享红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,分享红包,单个红包金额,红包发放个数
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,分享红包,6kb,66
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">口令红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,口令红包,口令内容,单个红包金额,红包发放个数
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,口令红包,快乐,6kb,66
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h5 class="font-semibold text-green-400 mb-2">幸运助手格式：</h5>

                            <div class="space-y-2">
                                <div>
                                    <p class="text-xs text-gray-300 mb-1">观看时长：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,附加条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,观看时长,1min,100人,1/4,5min,2025-08-30 18:00:00,奖品
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">评论：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,附加条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,评论,快乐,100人,1/4,5min,2025-08-30 18:00:00,奖品
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">粉丝团：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,附加条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,粉丝团,LV3,100人,1/4,5min,2025-08-30 18:00:00,奖品
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">超粉团：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,附加条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,超粉团,金粉,100人,1/4,5min,2025-08-30 18:00:00,奖品
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">其他条件（点赞、分享、关注主播）：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                    <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                        <strong>范例：</strong>Before Party,点赞,100人,1/4,5min,2025-08-30 18:00:00,奖品
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-xs text-gray-400">
                            <p><strong>注意事项：</strong></p>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>时间格式：2025-08-30 18:00:00</li>
                                <li>轮次格式：1/4（当前/总数）</li>
                                <li>中奖人数必须带"人"字</li>
                                <li>每行一个配置</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 右侧：输入区域 -->
                    <div>
                        <label class="block text-sm font-medium mb-2">批量导入内容：</label>
                        <textarea id="batchImportText" rows="12"
                                  class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm"
                                  placeholder="请按照左侧格式输入产品配置，每行一个配置..."></textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.processBatchImport()"
                            class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                        开始导入
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('批量导入', modalContent);
    }

    // 显示清空配置模态框
    showClearConfigModal() {
        const products = window.storageManager.getProducts();
        const productCount = products.length;

        if (productCount === 0) {
            this.showMessage('当前没有配置项需要清空', 'info');
            return;
        }

        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-red-400 mb-2">⚠️ 清空配置确认</h4>
                    <p class="text-sm text-gray-300">您即将清空所有产品配置，此操作不可撤销！</p>
                    <div class="mt-2 p-3 bg-red-900 rounded text-red-200 text-sm">
                        <strong>当前共有 ${productCount} 个配置项将被删除</strong>
                    </div>
                </div>

                <div class="bg-yellow-900 p-3 rounded text-yellow-200 text-sm">
                    <strong>⚠️ 安全确认：</strong>请在下方输入框中输入"确认"二字，然后点击确认按钮执行清空操作。
                </div>

                <div>
                    <label class="block text-sm font-medium mb-2">确认输入：</label>
                    <input type="text" id="clearConfirmInput"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="请输入"确认"二字">
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.processClearConfig()"
                            class="bg-red-600 hover:bg-red-700 px-6 py-2 rounded transition-colors">
                        确认清空
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('清空配置', modalContent);
    }

    // 处理清空配置
    processClearConfig() {
        const confirmInput = document.getElementById('clearConfirmInput');
        if (!confirmInput) {
            this.showMessage('确认输入框未找到', 'error');
            return;
        }

        const inputValue = confirmInput.value.trim();
        if (inputValue !== '确认') {
            this.showMessage('请输入"确认"二字以执行清空操作', 'error');
            return;
        }

        // 执行清空操作
        const products = window.storageManager.getProducts();
        const productCount = products.length;

        // 清空所有产品配置
        window.storageManager.clearAllProducts();

        // 刷新界面
        this.loadProducts();

        // 通知Dashboard更新
        if (window.dashboardManager) {
            window.dashboardManager.updateDashboard();
        }

        // 关闭模态框
        window.app.hideModal();

        // 显示成功消息
        this.showMessage(`成功清空 ${productCount} 个配置项`, 'success');
    }

    // 显示批量修改模态框
    showBatchEditModal() {
        const products = window.storageManager.getProducts();
        const rooms = window.storageManager.getRooms();
        const roomsText = rooms.join('、');

        if (products.length === 0) {
            this.showMessage('当前没有配置项可以修改', 'info');
            return;
        }

        // 将当前配置转换为批量导入格式
        const currentConfigText = this.convertProductsToImportFormat(products);

        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-indigo-400 mb-2">批量修改产品配置</h4>
                    <p class="text-sm text-gray-300">当前配置已加载到输入框中，您可以直接修改。修改完成后点击"保存修改"。</p>
                    <div class="mt-2 p-2 bg-blue-900 rounded text-blue-200 text-sm">
                        <strong>📋 当前可用直播间：</strong>${roomsText}
                    </div>
                </div>

                <!-- 横向布局：左侧格式说明，右侧输入区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 左侧：格式说明 -->
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <div>
                            <h5 class="font-semibold text-orange-400 mb-2">红包格式：</h5>

                            <div class="space-y-2">
                                <div>
                                    <p class="text-xs text-gray-300 mb-1">条件型普通快币红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,条件型普通快币红包,快币数
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">粉丝团红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,粉丝团红包,粉丝团红包等级,单个红包金额,红包发放个数
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">分享红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,分享红包,单个红包金额,红包发放个数
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">口令红包：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,发送时间,开奖时间,轮次,口令红包,口令内容,单个红包金额,红包发放个数
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h5 class="font-semibold text-green-400 mb-2">幸运助手格式：</h5>

                            <div class="space-y-2">
                                <div>
                                    <p class="text-xs text-gray-300 mb-1">观看时长、评论、粉丝团、超粉团：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,附加条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                </div>

                                <div>
                                    <p class="text-xs text-gray-300 mb-1">其他条件（点赞、分享、关注主播）：</p>
                                    <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                        直播间名,参与条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-xs text-gray-400">
                            <p><strong>修改说明：</strong></p>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>直接修改输入框中的内容</li>
                                <li>可以删除不需要的行</li>
                                <li>可以添加新的配置行</li>
                                <li>保存后将替换所有当前配置</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 右侧：输入区域 -->
                    <div>
                        <label class="block text-sm font-medium mb-2">当前配置（可修改）：</label>
                        <textarea id="batchEditText" rows="12"
                                  class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm"
                                  placeholder="配置内容将在这里显示...">${currentConfigText}</textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.processBatchEdit()"
                            class="bg-indigo-600 hover:bg-indigo-700 px-6 py-2 rounded transition-colors">
                        保存修改
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('批量修改', modalContent);
    }

    // 将产品配置转换为批量导入格式
    convertProductsToImportFormat(products) {
        return products.map(product => {
            if (product.type === 'redpack') {
                const participationType = this.getParticipationTypeText(product.participationType);
                let line = `${product.room},${product.sendTime},${product.drawTime}min,${product.currentRound}/${product.totalRounds},${participationType}`;

                // 根据参与方式添加附加字段
                if (product.participationType === 'condition') {
                    line += `,${product.coins || 0}`;
                } else if (product.participationType === 'fanclub') {
                    line += `,${product.fanclubLevel || '粉丝团红包'},${product.amount || ''},${product.count || 0}`;
                } else if (product.participationType === 'share') {
                    line += `,${product.amount || ''},${product.count || 0}`;
                } else if (product.participationType === 'password') {
                    line += `,${product.password || ''},${product.amount || ''},${product.count || 0}`;
                }

                return line;
            } else if (product.type === 'lucky') {
                const condition = this.getConditionText(product.condition);
                let line = `${product.room},${condition}`;

                // 根据参与条件添加附加字段
                if (['watchtime', 'comment', 'fanclub', 'superfan'].includes(product.condition)) {
                    let additionalCondition = '';
                    if (product.condition === 'watchtime') {
                        additionalCondition = `${product.watchtime || 0}min`;
                    } else if (product.condition === 'comment') {
                        additionalCondition = product.commentPassword || '';
                    } else if (product.condition === 'fanclub') {
                        additionalCondition = product.fanclubLevel || 'LV1';
                    } else if (product.condition === 'superfan') {
                        additionalCondition = product.superfanType || '金粉';
                    }
                    line += `,${additionalCondition},${product.winners}人,${product.currentRound}/${product.totalRounds},${product.countdown}min,${product.scheduledTime},${product.prizeDescription || '奖品'}`;
                } else {
                    line += `,${product.winners}人,${product.currentRound}/${product.totalRounds},${product.countdown}min,${product.scheduledTime},${product.prizeDescription || '奖品'}`;
                }

                return line;
            }
            return '';
        }).filter(line => line).join('\n');
    }

    // 处理批量修改
    processBatchEdit() {
        const text = document.getElementById('batchEditText').value.trim();
        if (!text) {
            this.showMessage('请输入修改内容', 'error');
            return;
        }

        // 先清空所有现有配置
        window.storageManager.clearAllProducts();

        const lines = text.split('\n').filter(line => line.trim());
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            try {
                const result = this.parseBatchImportLine(line, i + 1);
                if (result.success) {
                    const productId = window.storageManager.addProduct(result.product);
                    if (productId) {
                        successCount++;
                    } else {
                        errorCount++;
                        errors.push(`第${i + 1}行：保存失败`);
                    }
                } else {
                    errorCount++;
                    errors.push(`第${i + 1}行：${result.error}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(`第${i + 1}行：解析失败 - ${error.message}`);
            }
        }

        // 显示结果
        let message = `批量修改完成：成功 ${successCount} 个`;
        if (errorCount > 0) {
            message += `，失败 ${errorCount} 个`;
        }

        if (errors.length > 0 && errors.length <= 5) {
            message += '\n错误详情：\n' + errors.join('\n');
        } else if (errors.length > 5) {
            message += '\n错误详情（前5个）：\n' + errors.slice(0, 5).join('\n') + '\n...';
        }

        this.showMessage(message, errorCount > 0 ? 'error' : 'success');

        if (successCount > 0) {
            this.loadProducts();
            // 通知Dashboard更新
            if (window.dashboardManager) {
                window.dashboardManager.updateDashboard();
            }
        }

        window.app.hideModal();
    }

    // 处理批量导入
    processBatchImport() {
        const text = document.getElementById('batchImportText').value.trim();
        if (!text) {
            this.showMessage('请输入导入内容', 'error');
            return;
        }

        const lines = text.split('\n').filter(line => line.trim());
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            try {
                const result = this.parseBatchImportLine(line, i + 1);
                if (result.success) {
                    const productId = window.storageManager.addProduct(result.product);
                    if (productId) {
                        successCount++;
                    } else {
                        errorCount++;
                        errors.push(`第${i + 1}行：保存失败`);
                    }
                } else {
                    errorCount++;
                    errors.push(`第${i + 1}行：${result.error}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(`第${i + 1}行：解析失败 - ${error.message}`);
            }
        }

        // 显示结果
        let message = `导入完成：成功 ${successCount} 个`;
        if (errorCount > 0) {
            message += `，失败 ${errorCount} 个`;
        }

        if (errors.length > 0 && errors.length <= 5) {
            message += '\n错误详情：\n' + errors.join('\n');
        } else if (errors.length > 5) {
            message += '\n错误详情（前5个）：\n' + errors.slice(0, 5).join('\n') + '\n...';
        }

        this.showMessage(message, errorCount > 0 ? 'error' : 'success');

        if (successCount > 0) {
            this.loadProducts();
            // 通知Dashboard更新
            if (window.dashboardManager) {
                window.dashboardManager.updateDashboard();
            }
        }

        window.app.hideModal();
    }

    // 解析批量导入的单行数据
    parseBatchImportLine(line, lineNumber) {
        const parts = line.split(',').map(part => part.trim());

        // 根据字段数量和内容判断类型
        if (parts.length >= 6) {
            // 检查是否为红包类型
            const participationType = parts[4];
            if (['粉丝团红包', '分享红包', '条件型普通快币红包', '口令红包'].includes(participationType)) {
                return this.parseRedpackLine(parts, lineNumber);
            }
            // 否则尝试解析为幸运助手
            return this.parseLuckyLine(parts, lineNumber);
        } else {
            return {
                success: false,
                error: `格式错误，字段数量不足，实际为${parts.length}个字段`
            };
        }
    }

    // 解析红包行
    parseRedpackLine(parts, lineNumber) {
        const room = parts[0];
        const sendTime = parts[1];
        const drawTime = parts[2];
        const rounds = parts[3];
        const participationType = parts[4];

        // 验证直播间
        const rooms = window.storageManager.getRooms();
        if (!rooms.includes(room)) {
            return { success: false, error: `直播间"${room}"不存在` };
        }

        // 验证时间格式
        if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(sendTime)) {
            return { success: false, error: `发送时间格式错误，应为"2025-08-30 18:00:00"` };
        }

        // 验证开奖时间
        const drawTimeMatch = drawTime.match(/^(\d+)min$/);
        if (!drawTimeMatch || !['3', '10'].includes(drawTimeMatch[1])) {
            return { success: false, error: `开奖时间格式错误，应为"3min"或"10min"` };
        }

        // 验证轮次
        const roundsMatch = rounds.match(/^(\d+)\/(\d+)$/);
        if (!roundsMatch) {
            return { success: false, error: `轮次格式错误，应为"1/4"格式` };
        }

        // 验证参与方式
        const participationTypes = {
            '粉丝团红包': 'fanclub',
            '分享红包': 'share',
            '条件型普通快币红包': 'condition',
            '口令红包': 'password'
        };
        if (!participationTypes[participationType]) {
            return { success: false, error: `参与方式"${participationType}"不支持` };
        }

        let product = {
            room: room,
            type: 'redpack',
            sendTime: sendTime,
            drawTime: parseInt(drawTimeMatch[1]),
            currentRound: parseInt(roundsMatch[1]),
            totalRounds: parseInt(roundsMatch[2]),
            participationType: participationTypes[participationType],
            coins: 0,
            fanclubLevel: '',
            amount: '',
            count: 0,
            password: ''
        };

        // 根据参与方式解析不同的附加字段
        if (participationType === '条件型普通快币红包') {
            // 格式：直播间名,发送时间,开奖时间,轮次,条件型普通快币红包,快币数
            if (parts.length !== 6) {
                return { success: false, error: `条件型普通快币红包格式错误，应为6个字段` };
            }
            const coins = parseInt(parts[5]);
            if (isNaN(coins) || coins < 0) {
                return { success: false, error: `快币数格式错误` };
            }
            product.coins = coins;
        } else if (participationType === '粉丝团红包') {
            // 格式：直播间名,发送时间,开奖时间,轮次,粉丝团红包,粉丝团红包等级,单个红包金额,红包发放个数
            if (parts.length !== 8) {
                return { success: false, error: `粉丝团红包格式错误，应为8个字段` };
            }
            const fanclubLevel = parts[5];
            const amount = parts[6];
            const count = parseInt(parts[7]);
            if (!['粉丝团红包', '金粉团红包', '钻粉团红包', '超粉团红包'].includes(fanclubLevel)) {
                return { success: false, error: `粉丝团红包等级"${fanclubLevel}"不支持` };
            }
            if (isNaN(count) || count < 1) {
                return { success: false, error: `红包发放个数格式错误` };
            }
            product.fanclubLevel = fanclubLevel;
            product.amount = amount;
            product.count = count;
        } else if (participationType === '分享红包') {
            // 格式：直播间名,发送时间,开奖时间,轮次,分享红包,单个红包金额,红包发放个数
            if (parts.length !== 7) {
                return { success: false, error: `分享红包格式错误，应为7个字段` };
            }
            const amount = parts[5];
            const count = parseInt(parts[6]);
            if (isNaN(count) || count < 1) {
                return { success: false, error: `红包发放个数格式错误` };
            }
            product.amount = amount;
            product.count = count;
        } else if (participationType === '口令红包') {
            // 格式：直播间名,发送时间,开奖时间,轮次,口令红包,口令内容,单个红包金额,红包发放个数
            if (parts.length !== 8) {
                return { success: false, error: `口令红包格式错误，应为8个字段` };
            }
            const password = parts[5];
            const amount = parts[6];
            const count = parseInt(parts[7]);
            if (isNaN(count) || count < 1) {
                return { success: false, error: `红包发放个数格式错误` };
            }
            product.password = password;
            product.amount = amount;
            product.count = count;
        }

        return {
            success: true,
            product: product
        };
    }

    // 解析幸运助手行
    parseLuckyLine(parts, lineNumber) {
        const room = parts[0];
        const condition = parts[1];

        // 验证直播间
        const rooms = window.storageManager.getRooms();
        if (!rooms.includes(room)) {
            return { success: false, error: `直播间"${room}"不存在` };
        }

        // 验证参与条件
        const conditions = {
            '评论': 'comment',
            '点赞': 'like',
            '粉丝团': 'fanclub',
            '分享': 'share',
            '关注主播': 'follow',
            '观看时长': 'watchtime',
            '超粉团': 'superfan'
        };
        if (!conditions[condition]) {
            return { success: false, error: `参与条件"${condition}"不支持` };
        }

        let product = {
            room: room,
            type: 'lucky',
            condition: conditions[condition],
            winners: 0,
            currentRound: 0,
            totalRounds: 0,
            countdown: 0,
            scheduledTime: '',
            prizeDescription: '',
            watchtime: 0,
            commentPassword: '',
            fanclubLevel: '',
            superfanType: ''
        };

        // 根据参与条件解析不同的格式
        if (['观看时长', '评论', '粉丝团', '超粉团'].includes(condition)) {
            // 格式：直播间名,参与条件,附加条件,中奖人数,轮次,倒计时,发送时间,奖品说明
            if (parts.length !== 8) {
                return { success: false, error: `${condition}格式错误，应为8个字段` };
            }

            const additionalCondition = parts[2];
            const winners = parts[3];
            const rounds = parts[4];
            const countdown = parts[5];
            const scheduledTime = parts[6];
            const prizeDescription = parts[7];

            // 验证中奖人数
            const winnersMatch = winners.match(/^(\d+)人$/);
            if (!winnersMatch) {
                return { success: false, error: `中奖人数格式错误，应为"100人"格式` };
            }

            // 验证轮次
            const roundsMatch = rounds.match(/^(\d+)\/(\d+)$/);
            if (!roundsMatch) {
                return { success: false, error: `轮次格式错误，应为"1/4"格式` };
            }

            // 验证开奖倒计时
            const countdownMatch = countdown.match(/^(\d+)min$/);
            if (!countdownMatch) {
                return { success: false, error: `开奖倒计时格式错误，应为"5min"格式` };
            }

            // 验证定时发送时间
            if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(scheduledTime)) {
                return { success: false, error: `定时发送时间格式错误，应为"2025-08-30 18:00:00"` };
            }

            product.winners = parseInt(winnersMatch[1]);
            product.currentRound = parseInt(roundsMatch[1]);
            product.totalRounds = parseInt(roundsMatch[2]);
            product.countdown = parseInt(countdownMatch[1]);
            product.scheduledTime = scheduledTime;
            product.prizeDescription = prizeDescription;

            // 根据条件类型验证和设置附加条件
            if (condition === '观看时长') {
                const watchtimeMatch = additionalCondition.match(/^(\d+)min$/);
                if (!watchtimeMatch) {
                    return { success: false, error: `观看时长格式错误，应为"1min"格式` };
                }
                const watchtime = parseInt(watchtimeMatch[1]);
                if (watchtime >= product.countdown) {
                    return { success: false, error: `观看时长必须小于开奖倒计时` };
                }
                product.watchtime = watchtime;
            } else if (condition === '评论') {
                if (additionalCondition.length > 8) {
                    return { success: false, error: `评论口令最多8个字符` };
                }
                product.commentPassword = additionalCondition;
            } else if (condition === '粉丝团') {
                if (!['LV1', 'LV2', 'LV3', 'LV4', 'LV5', 'LV6', 'LV7', 'LV8', 'LV9', 'LV10'].includes(additionalCondition)) {
                    return { success: false, error: `粉丝团等级"${additionalCondition}"不支持` };
                }
                product.fanclubLevel = additionalCondition;
            } else if (condition === '超粉团') {
                if (!['金粉', '钻粉', '超粉'].includes(additionalCondition)) {
                    return { success: false, error: `超粉团类型"${additionalCondition}"不支持` };
                }
                product.superfanType = additionalCondition;
            }
        } else {
            // 其他条件（点赞、分享、关注主播）：直播间名,参与条件,中奖人数,轮次,倒计时,发送时间,奖品说明
            if (parts.length !== 7) {
                return { success: false, error: `${condition}格式错误，应为7个字段` };
            }

            const winners = parts[2];
            const rounds = parts[3];
            const countdown = parts[4];
            const scheduledTime = parts[5];
            const prizeDescription = parts[6];

            // 验证中奖人数
            const winnersMatch = winners.match(/^(\d+)人$/);
            if (!winnersMatch) {
                return { success: false, error: `中奖人数格式错误，应为"100人"格式` };
            }

            // 验证轮次
            const roundsMatch = rounds.match(/^(\d+)\/(\d+)$/);
            if (!roundsMatch) {
                return { success: false, error: `轮次格式错误，应为"1/4"格式` };
            }

            // 验证开奖倒计时
            const countdownMatch = countdown.match(/^(\d+)min$/);
            if (!countdownMatch) {
                return { success: false, error: `开奖倒计时格式错误，应为"5min"格式` };
            }

            // 验证定时发送时间
            if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(scheduledTime)) {
                return { success: false, error: `定时发送时间格式错误，应为"2025-08-30 18:00:00"` };
            }

            product.winners = parseInt(winnersMatch[1]);
            product.currentRound = parseInt(roundsMatch[1]);
            product.totalRounds = parseInt(roundsMatch[2]);
            product.countdown = parseInt(countdownMatch[1]);
            product.scheduledTime = scheduledTime;
            product.prizeDescription = prizeDescription;
        }

        return {
            success: true,
            product: product
        };
    }

    // 滚动到产品配置表单
    scrollToProductForm() {
        setTimeout(() => {
            const productForm = document.getElementById('productForm');
            if (productForm && !productForm.classList.contains('hidden')) {
                productForm.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            }
        }, 100); // 延迟一点确保表单已经显示
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-all duration-300 transform translate-y-0 max-w-md ${
            type === 'success' ? 'bg-green-600' :
            type === 'error' ? 'bg-red-600' :
            'bg-blue-600'
        }`;

        // 处理多行消息
        if (message.includes('\n')) {
            messageDiv.innerHTML = message.split('\n').map(line => `<div>${line}</div>`).join('');
        } else {
            messageDiv.textContent = message;
        }

        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.className = 'ml-3 text-white hover:text-gray-200 font-bold text-lg float-right';
        closeBtn.onclick = () => {
            messageDiv.style.transform = 'translateY(100%)';
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        };
        messageDiv.appendChild(closeBtn);

        document.body.appendChild(messageDiv);

        // 入场动画
        setTimeout(() => {
            messageDiv.style.transform = 'translateY(0)';
        }, 10);

        // 10秒后自动移除（批量导入可能有很多错误信息）
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.transform = 'translateY(100%)';
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }
        }, 10000);
    }
}

// 创建全局配置管理器实例
window.configManager = new ConfigManager();
