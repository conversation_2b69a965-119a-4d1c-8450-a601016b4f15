<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动滚动到表单测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .header-frozen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(75, 85, 99, 0.5);
        }
        
        .main-content-with-frozen-header {
            /* 动态设置padding-top */
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 测试头部 -->
    <header id="mainHeader" class="bg-gray-800 border-b border-gray-700 p-4 header-frozen">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-2xl font-bold text-blue-400">自动滚动到表单测试</h1>
            <div class="mt-2 flex space-x-4">
                <button onclick="toggleHeaderFreeze()" class="bg-purple-600 hover:bg-purple-700 px-3 py-2 rounded">
                    切换头部冻结
                </button>
                <span id="freezeStatus" class="px-3 py-2 bg-gray-700 rounded">冻结模式：开启</span>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main id="mainContent" class="max-w-7xl mx-auto p-6 main-content-with-frozen-header">
        <!-- 填充内容 -->
        <div class="space-y-8">
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4">页面顶部内容</h2>
                <p class="text-gray-300">这里是一些填充内容，用于测试滚动效果。</p>
            </div>

            <!-- 产品配置区域 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-400">产品配置</h2>
                <div class="space-y-4">
                    <select id="selectedRoom" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="">选择直播间</option>
                        <option value="测试直播间1">测试直播间1</option>
                        <option value="测试直播间2">测试直播间2</option>
                    </select>
                    <div class="flex space-x-2">
                        <select id="productType" class="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                            <option value="">选择产品类型</option>
                            <option value="redpack">红包</option>
                            <option value="lucky">幸运助手</option>
                        </select>
                        <button onclick="showProductForm()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors">
                            添加产品
                        </button>
                    </div>
                </div>
            </div>

            <!-- 更多填充内容 -->
            <div class="space-y-4">
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 1</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 2</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 3</div>
            </div>

            <!-- 产品配置表单 -->
            <div id="productForm" class="hidden bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-yellow-400" id="formTitle">
                    产品配置表单
                </h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">配置项 1</label>
                            <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">配置项 2</label>
                            <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">配置项 3</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                                <option>选项 1</option>
                                <option>选项 2</option>
                                <option>选项 3</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">配置项 4</label>
                            <input type="number" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">详细配置</label>
                        <textarea class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-24"></textarea>
                    </div>
                    <div class="flex space-x-4 mt-6">
                        <button class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                            保存配置
                        </button>
                        <button onclick="hideProductForm()" class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            </div>

            <!-- 更多填充内容 -->
            <div class="space-y-4">
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 4</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 5</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 6</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 7</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">填充内容 8</div>
            </div>
        </div>
    </main>

    <script>
        let headerFrozen = true;

        // 页面加载后调整主内容区域的padding
        function adjustMainContentPadding() {
            const header = document.getElementById('mainHeader');
            const mainContent = document.getElementById('mainContent');
            
            if (header && mainContent) {
                if (headerFrozen) {
                    const headerHeight = header.offsetHeight;
                    mainContent.style.paddingTop = `${headerHeight + 20}px`;
                } else {
                    mainContent.style.paddingTop = '24px';
                }
            }
        }

        // 切换头部冻结模式
        function toggleHeaderFreeze() {
            headerFrozen = !headerFrozen;
            const header = document.getElementById('mainHeader');
            const freezeStatus = document.getElementById('freezeStatus');
            
            if (headerFrozen) {
                header.classList.add('header-frozen');
                freezeStatus.textContent = '冻结模式：开启';
            } else {
                header.classList.remove('header-frozen');
                freezeStatus.textContent = '冻结模式：关闭';
            }
            
            adjustMainContentPadding();
        }

        // 显示产品配置表单
        function showProductForm() {
            const selectedRoom = document.getElementById('selectedRoom').value;
            const productType = document.getElementById('productType').value;
            const formTitle = document.getElementById('formTitle');
            
            if (!selectedRoom) {
                alert('请先选择直播间');
                return;
            }
            
            if (!productType) {
                alert('请选择产品类型');
                return;
            }
            
            // 更新表单标题
            const typeName = productType === 'redpack' ? '红包配置' : '幸运助手配置';
            formTitle.textContent = `${typeName} - ${selectedRoom}`;
            
            // 显示表单
            document.getElementById('productForm').classList.remove('hidden');
            
            // 滚动到表单区域（居中显示）
            scrollToProductForm();
        }

        // 隐藏产品配置表单
        function hideProductForm() {
            document.getElementById('productForm').classList.add('hidden');
        }

        // 滚动到产品配置表单区域（居中显示）
        function scrollToProductForm() {
            const productForm = document.getElementById('productForm');
            const header = document.getElementById('mainHeader');
            
            if (productForm && header) {
                // 等待表单内容渲染完成后再滚动
                setTimeout(() => {
                    if (headerFrozen) {
                        // 冻结模式下，计算居中位置
                        const headerHeight = header.offsetHeight;
                        const viewportHeight = window.innerHeight;
                        const formHeight = productForm.offsetHeight;
                        const formTop = productForm.offsetTop;
                        
                        // 计算让表单在可视区域居中的位置
                        const availableHeight = viewportHeight - headerHeight;
                        const centerOffset = (availableHeight - formHeight) / 2;
                        const targetPosition = formTop - headerHeight - Math.max(centerOffset, 20);
                        
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    } else {
                        // 非冻结模式下，使用scrollIntoView居中显示
                        productForm.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }
                }, 100); // 等待100ms让表单内容完全渲染
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', adjustMainContentPadding);
        window.addEventListener('resize', adjustMainContentPadding);
    </script>
</body>
</html>
