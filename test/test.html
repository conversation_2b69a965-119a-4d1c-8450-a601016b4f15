<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒计时测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'breathing': 'breathing 2s ease-in-out infinite',
                    },
                    keyframes: {
                        breathing: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.3' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .breathing-text {
            animation: breathing 2s ease-in-out infinite;
        }
        .breathing-container {
            animation: breathing 2s ease-in-out infinite;
        }
        @keyframes pulse-red {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
        }
        .pulse-red {
            animation: pulse-red 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-400">倒计时功能测试</h1>
        
        <!-- 测试倒计时 -->
        <div class="space-y-6">
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-orange-400">红包倒计时测试</h2>
                <div class="flex justify-between items-center">
                    <div>
                        <span class="text-gray-400">产品：</span>
                        <span class="text-xl font-semibold ml-2 text-orange-400">红包 (测试直播间)</span>
                    </div>
                    <div id="redpackCountdown" class="text-3xl font-bold font-mono text-yellow-400">
                        00:01:30
                    </div>
                </div>
                <div class="mt-2 text-sm text-gray-300">
                    <span class="mr-4">直播间：测试直播间</span>
                    <span>详情：第1/5轮 | 1000快币</span>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-400">幸运助手倒计时测试</h2>
                <div class="flex justify-between items-center">
                    <div>
                        <span class="text-gray-400">产品：</span>
                        <span class="text-xl font-semibold ml-2 text-green-400">幸运助手 (夏晚主会场)</span>
                    </div>
                    <div id="luckyCountdown" class="text-3xl font-bold font-mono text-yellow-400">
                        00:05:00
                    </div>
                </div>
                <div class="mt-2 text-sm text-gray-300">
                    <span class="mr-4">直播间：夏晚主会场</span>
                    <span>详情：评论 | 10人中奖</span>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-purple-400">控制面板</h2>
                <div class="space-x-4">
                    <button onclick="setCountdown('redpack', 90)" class="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded">
                        红包设为1分30秒
                    </button>
                    <button onclick="setCountdown('redpack', 30)" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                        红包设为30秒（警告状态）
                    </button>
                    <button onclick="setCountdown('lucky', 300)" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                        幸运助手设为5分钟
                    </button>
                    <button onclick="setCountdown('lucky', 45)" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                        幸运助手设为45秒（警告状态）
                    </button>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <a href="/" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors">
                返回主应用
            </a>
        </div>
    </div>

    <script>
        let redpackTime = 90; // 1分30秒
        let luckyTime = 300; // 5分钟
        
        function updateCountdown() {
            // 更新红包倒计时
            const redpackElement = document.getElementById('redpackCountdown');
            const redpackParent = redpackElement.parentElement.parentElement;
            
            if (redpackTime > 0) {
                const minutes = Math.floor(redpackTime / 60);
                const seconds = redpackTime % 60;
                const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                redpackElement.textContent = timeStr;
                
                // 剩余1分钟时变为红色呼吸灯效果
                if (redpackTime <= 60) {
                    redpackElement.className = 'text-3xl font-bold font-mono text-red-500 breathing-text';
                    redpackParent.className = 'bg-red-900 border-2 border-red-500 rounded-lg p-6 breathing-container pulse-red';
                } else {
                    redpackElement.className = 'text-3xl font-bold font-mono text-yellow-400';
                    redpackParent.className = 'bg-gray-800 rounded-lg p-6';
                }
                
                redpackTime--;
            } else {
                redpackElement.textContent = '已过期';
                redpackElement.className = 'text-3xl font-bold font-mono text-red-400';
                redpackParent.className = 'bg-gray-600 opacity-60 rounded-lg p-6';
            }
            
            // 更新幸运助手倒计时
            const luckyElement = document.getElementById('luckyCountdown');
            const luckyParent = luckyElement.parentElement.parentElement;
            
            if (luckyTime > 0) {
                const minutes = Math.floor(luckyTime / 60);
                const seconds = luckyTime % 60;
                const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                luckyElement.textContent = timeStr;
                
                // 剩余1分钟时变为红色呼吸灯效果
                if (luckyTime <= 60) {
                    luckyElement.className = 'text-3xl font-bold font-mono text-red-500 breathing-text';
                    luckyParent.className = 'bg-red-900 border-2 border-red-500 rounded-lg p-6 breathing-container pulse-red';
                } else {
                    luckyElement.className = 'text-3xl font-bold font-mono text-yellow-400';
                    luckyParent.className = 'bg-gray-800 rounded-lg p-6';
                }
                
                luckyTime--;
            } else {
                luckyElement.textContent = '已过期';
                luckyElement.className = 'text-3xl font-bold font-mono text-red-400';
                luckyParent.className = 'bg-gray-600 opacity-60 rounded-lg p-6';
            }
        }
        
        function setCountdown(type, seconds) {
            if (type === 'redpack') {
                redpackTime = seconds;
            } else if (type === 'lucky') {
                luckyTime = seconds;
            }
        }
        
        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
        
        // 初始更新
        updateCountdown();
    </script>
</body>
</html>
