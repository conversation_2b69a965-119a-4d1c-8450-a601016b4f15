<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .breathing-text {
            animation: breathing 2s ease-in-out infinite;
        }
        .breathing-container {
            animation: breathing 2s ease-in-out infinite;
        }
        @keyframes breathing {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        @keyframes pulse-red {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
        }
        .pulse-red {
            animation: pulse-red 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-blue-400 mb-8">修改功能测试</h1>
        
        <!-- 测试区域1：幸运助手详细信息顺序 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-green-400 mb-4">1. 幸运助手详细信息顺序测试</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">原来的顺序：第1/4轮 | 评论 | 100人中奖 | 5分钟倒计时</div>
                <div class="text-sm text-yellow-300">修改后的顺序：第1/4轮 | 100人中奖 | 评论 | 5分钟倒计时</div>
            </div>
        </div>

        <!-- 测试区域2：紧急上线文本 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-orange-400 mb-4">2. 紧急上线文本修改测试</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">原来的文本：🚨 紧急上线</div>
                <div class="text-sm text-yellow-300">修改后的文本：🚨 准备上线</div>
            </div>
        </div>

        <!-- 测试区域3：下一个即将上线逻辑 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-cyan-400 mb-4">3. 下一个即将上线逻辑测试</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">修改前：会显示"挂件上线中"状态的产品</div>
                <div class="text-sm text-yellow-300">修改后：不展示处于"挂件上线中"状态下的产品</div>
            </div>
        </div>

        <!-- 测试区域4：多产品轮播功能 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-purple-400 mb-4">4. 多产品轮播功能测试</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">新增功能：当有多个产品符合"下一个即将上线"时</div>
                <div class="text-sm text-yellow-300">• 每隔5秒从上到下翻页循环轮播展示</div>
                <div class="text-sm text-yellow-300">• 展示"🚨 注意同时上线"</div>
                <div class="text-sm text-yellow-300">• 时间模块固定，不跟随循环轮播翻页</div>
            </div>
        </div>

        <!-- 测试区域5：批量导入功能 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-pink-400 mb-4">5. 批量导入功能测试</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="text-lg font-semibold text-orange-400 mb-2">红包批量导入格式：</h3>
                    <div class="bg-gray-700 p-3 rounded text-sm font-mono text-gray-300">
                        直播间名,红包发送时间（格式2025-08-30 18:00:00）,开奖时间(3min),当前轮次和总轮次(以"/"为分割),参与方式,快币数
                    </div>
                    <div class="bg-gray-600 p-2 rounded text-sm text-yellow-300 mt-2">
                        <strong>提示范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,66
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-green-400 mb-2">幸运助手批量导入格式：</h3>
                    <div class="bg-gray-700 p-3 rounded text-sm font-mono text-gray-300">
                        直播间名,参与条件,中奖人数（必须标注"人"字）,当前轮次和总轮次(以"/"为分割),开奖倒计时（5min）,定时发送时间（格式2025-08-30 18:00:00）,奖品说明
                    </div>
                    <div class="bg-gray-600 p-2 rounded text-sm text-yellow-300 mt-2">
                        <strong>提示范例：</strong>Before Party,评论,100人,1/4,5min,2025-08-30 18:00:00,奖品
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-blue-400 mb-4">测试操作</h2>
            <div class="space-x-4">
                <button onclick="window.open('index.html', '_blank')" 
                        class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded transition-colors">
                    打开主应用测试
                </button>
                <button onclick="testBatchImport()" 
                        class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                    测试批量导入格式
                </button>
            </div>
        </div>
    </div>

    <script>
        function testBatchImport() {
            const testData = `Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,66
Before Party,评论,100人,1/4,5min,2025-08-30 18:30:00,奖品说明
夏晚主会场（横屏）,2025-08-30 19:00:00,10min,2/4,分享红包,88
夏晚主会场（横屏）,点赞,50人,2/4,3min,2025-08-30 19:30:00,精美礼品`;
            
            alert('测试数据示例：\n\n' + testData + '\n\n请在主应用的批量导入功能中测试此数据。');
        }
    </script>
</body>
</html>
