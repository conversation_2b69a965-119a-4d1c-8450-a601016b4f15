# 直播间功能配置上线提醒大屏

一个专为直播间功能配置管理和上线提醒而设计的Web应用，支持红包和幸运助手产品的配置管理，提供实时提醒和时间线展示功能。

## 功能特性

### 🎯 核心功能
- **实时时间显示**：显示北京时间，精确到秒
- **即将上线提醒**：顶部显示下一个即将上线的产品，剩余1分钟时红色呼吸灯闪烁
- **多直播间支持**：支持多个直播间的并行管理
- **产品配置管理**：支持红包和幸运助手两种产品类型
- **时间线展示**：按时间顺序展示所有配置项
- **数据持久化**：配置自动保存到浏览器本地存储

### 📺 直播间管理
- **默认直播间**：Before Party、夏晚主会场（横屏）、夏晚主会场（竖屏）、夏晚第二现场
- **自定义直播间**：支持添加和删除自定义直播间
- **直播间名称放大展示**：便于快速识别

### 🎁 红包产品配置
- **发送时间**：yyyy-mm-dd hh:mm 格式
- **开奖时间**：3分钟或10分钟选项
- **轮次管理**：第x/y轮显示
- **参与方式**：
  - 粉丝团红包
  - 分享红包
  - 条件型普通快币红包
  - 口令红包
- **快币数**：0-99999999范围

### 🍀 幸运助手配置
- **参与条件**：评论、点赞、粉丝团、分享、关注主播、观看时长、超粉团
- **中奖人数**：可自定义
- **奖品说明**：详细描述
- **开奖倒计时**：1-60分钟
- **定时发送时间**：精确到分钟
- **限制条件验证**：
  - 幸运星活动间隔必须大于1分钟
  - 观看时长类幸运星，开奖倒计时必须大于观看时长

### 📊 提醒界面
- **Dashboard区**：核心信息展示，支持多直播间并行提醒
- **详细信息区**：列表形式展示所有配置项
- **紧急提醒**：
  - 5分钟内：橙色警告
  - 1分钟内：红色呼吸灯闪烁
  - 已过期：灰色显示

### 💾 数据管理
- **本地存储**：使用localStorage自动保存
- **导出功能**：保存为.ksconf格式文件
- **导入功能**：支持.ksconf文件导入
- **数据验证**：确保配置数据的完整性和正确性

## 使用方法

### 1. 打开应用
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 配置管理
1. 点击顶部的"配置管理"按钮
2. 在"直播间管理"区域添加或删除直播间
3. 选择直播间和产品类型，点击"添加产品"
4. 填写产品配置信息并保存

### 3. 提醒大屏
1. 点击顶部的"提醒大屏"按钮
2. 查看Dashboard区的核心提醒信息
3. 在详细时间线中查看所有配置项

### 4. 数据管理
- **保存配置**：点击"保存配置"按钮导出.ksconf文件
- **加载配置**：点击"加载配置"按钮导入.ksconf文件
- **自动保存**：所有配置会自动保存到浏览器本地存储

## 快捷键

- `Ctrl/Cmd + 1`：切换到提醒大屏
- `Ctrl/Cmd + 2`：切换到配置管理
- `Ctrl/Cmd + S`：导出配置
- `Ctrl/Cmd + R`：刷新数据
- `Esc`：关闭模态框

## 技术栈

- **HTML5**：页面结构
- **Tailwind CSS**：样式框架
- **JavaScript ES6+**：应用逻辑
- **LocalStorage**：数据持久化

## 浏览器兼容性

推荐使用以下浏览器的最新版本：
- Google Chrome
- Mozilla Firefox
- Safari
- Microsoft Edge

## 注意事项

1. **时间格式**：所有时间均为北京时间
2. **数据安全**：配置数据存储在浏览器本地，清除浏览器数据会丢失配置
3. **网络要求**：需要网络连接以加载Tailwind CSS和字体
4. **屏幕分辨率**：建议使用1920x1080或更高分辨率的显示器

## 文件结构

```
├── index.html                    # 主应用页面
├── script.js                     # 主应用脚本
├── config.js                     # 配置管理模块
├── dashboard.js                  # Dashboard显示模块
├── storage.js                    # 本地存储管理模块
├── README.md                     # 项目说明文档
├── MODIFICATIONS_SUMMARY.md      # 修改总结文档
└── test/                         # 测试文件夹
    ├── README.md                 # 测试文件夹说明
    ├── test.html                 # 基础功能测试页面
    ├── test-modifications.html   # 修改功能测试页面
    ├── test-new-modifications.html # 新修改功能测试页面
    ├── test-final-modifications.html # 最终修改功能测试页面
    ├── breathing-demo.html       # 呼吸灯效果演示
    ├── optimization-demo.html    # 性能优化演示
    ├── status-demo.html          # 状态显示演示
    ├── test-config.ksconf        # 基础测试配置文件
    ├── optimized-test-config.ksconf # 优化测试配置文件
    ├── status-test-config.ksconf # 状态测试配置文件
    ├── urgent-test-config.ksconf # 紧急测试配置文件
    └── batch-import-test.txt     # 批量导入测试数据
```

## 更新日志

### v1.0.0 (2025-08-24)
- 初始版本发布
- 支持红包和幸运助手产品配置
- 实现实时提醒和时间线功能
- 添加数据导入导出功能
- 完善用户界面和交互体验

## 支持

如有问题或建议，请联系开发团队。
