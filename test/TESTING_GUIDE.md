# 新功能测试指南

## 测试环境准备
1. 打开浏览器访问 `index.html`
2. 确保浏览器支持现代CSS和JavaScript特性

## 功能测试清单

### ✅ 功能1：头部冻结模式测试

**测试步骤**:
1. 打开主页面
2. 滚动页面向下
3. 观察头部区域是否保持固定

**预期结果**:
- 头部信息栏（包括标题、时间、导航按钮）保持固定在页面顶部
- "下一个即将上线"模块也保持固定
- 主内容区域正常滚动，不被头部遮挡
- 头部有半透明毛玻璃效果
- 在不同屏幕尺寸下都能正确显示

**验证要点**:
- 头部完全固定，不随页面滚动
- 主内容有足够的上边距，不被头部遮挡
- 调整浏览器窗口大小时，布局自动适应

---

### ✅ 功能2：Mock北京时间（调试时间）测试

**测试步骤**:
1. 点击头部的"北京时间"按钮
2. 在弹出的模态框中选择一个未来时间
3. 点击"确认设置"
4. 观察时间显示变化
5. 刷新页面验证调试模式是否保持
6. 再次点击时间按钮，选择"是"退出测试模式

**预期结果**:
- 点击时间后弹出时间设置模态框
- 设置调试时间后：
  - 时间标签变为"调试时间："
  - 时间显示为彩虹色渐变效果
  - 系统内所有时间计算基于调试时间
- 刷新页面后调试模式保持
- 退出测试模式后回归正常北京时间显示

**验证要点**:
- 调试时间设置成功后有成功提示
- 彩虹渐变效果正常显示
- 页面刷新后调试模式状态保持
- 退出调试模式功能正常

---

### ✅ 功能3：增强配置加载测试

**测试步骤**:
1. 进入"配置管理"页面
2. 找到"当前配置"区域的"加载配置"按钮
3. 鼠标悬停在按钮上，观察下拉菜单
4. 测试"本地文件"选项（点击后应弹出文件选择框）
5. 测试"线上配置"选项（点击后应弹出URL输入框）
6. 在URL输入框中输入一个配置文件URL并测试加载

**预期结果**:
- "加载配置"按钮显示为下拉菜单样式（简洁设计，无▼符号）
- 悬停时显示两个选项："本地文件"和"线上配置"
- 点击"本地文件"弹出系统文件选择对话框
- 点击"线上配置"弹出URL输入模态框
- URL输入框支持输入在线配置文件地址
- 成功加载在线配置后有成功提示

**验证要点**:
- 下拉菜单样式正确显示
- 两种加载模式都能正常工作
- 网络请求错误有适当的错误提示

**测试URL示例**:
```
https://raw.githubusercontent.com/username/repo/main/config.ksconf
```

---

### ✅ 功能4：自动滚动到配置区域测试

**测试步骤**:
1. 进入"配置管理"页面
2. 滚动到页面顶部
3. 在"产品配置"区域：
   - 选择一个直播间
   - 选择产品类型（红包或幸运助手）
   - 点击"添加产品"按钮
4. 观察页面是否自动滚动

**预期结果**:
- 点击"添加产品"后页面自动平滑滚动
- 滚动目标为"当前配置"区域（配置列表）
- 滚动位置考虑了固定头部的高度，不会被遮挡
- 滚动动画流畅自然

**验证要点**:
- 自动滚动功能正常触发
- 滚动位置准确，目标区域完全可见
- 滚动动画使用smooth效果

---

## 综合功能测试

### 场景1：调试模式下的完整流程
1. 设置调试时间为未来某个时间
2. 添加一些产品配置，设置上线时间
3. 切换到"提醒大屏"查看倒计时是否基于调试时间
4. 验证所有时间相关功能都使用调试时间

### 场景2：配置管理完整流程
1. 使用线上配置加载功能导入配置
2. 添加新产品，验证自动滚动
3. 在调试模式下验证产品状态计算
4. 导出配置验证功能完整性

## 常见问题排查

### 问题1：头部冻结不生效
- 检查CSS是否正确加载
- 验证header元素是否有`header-frozen`类
- 检查浏览器是否支持`backdrop-filter`

### 问题2：调试时间不显示彩虹色
- 检查CSS动画是否支持
- 验证`debug-time-rainbow`类是否正确应用
- 检查浏览器是否支持`background-clip: text`

### 问题3：线上配置加载失败
- 检查URL是否可直接访问
- 验证CORS设置（某些网站可能有跨域限制）
- 检查网络连接
- 查看浏览器控制台错误信息

### 问题4：自动滚动不工作
- 检查目标元素是否存在
- 验证`scrollTo`方法是否支持
- 检查滚动位置计算是否正确

## 浏览器兼容性

**推荐浏览器**:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

**已知限制**:
- IE浏览器不支持（使用了现代CSS和JavaScript特性）
- 较老版本浏览器可能不支持`backdrop-filter`效果

## 性能注意事项

- 调试模式下时间更新频率为每秒一次，正常使用不会影响性能
- 彩虹渐变动画使用CSS动画，GPU加速，性能良好
- 线上配置加载使用fetch API，支持现代浏览器的网络优化
