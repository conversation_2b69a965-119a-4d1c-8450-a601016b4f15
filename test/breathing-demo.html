<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>呼吸灯效果演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'breathing': 'breathing 2s ease-in-out infinite',
                    },
                    keyframes: {
                        breathing: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.3' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .breathing-text {
            animation: breathing 2s ease-in-out infinite;
        }
        .breathing-container {
            animation: breathing 2s ease-in-out infinite;
        }
        @keyframes pulse-red {
            0%, 100% { 
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% { 
                box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
        }
        .pulse-red {
            animation: pulse-red 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-4xl font-bold mb-8 text-center text-blue-400">呼吸灯效果修复演示</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 修复前的问题演示 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-2xl font-semibold mb-4 text-red-400">❌ 修复前的问题</h2>
                <div class="space-y-4">
                    <div class="bg-red-900 border-2 border-red-500 rounded-lg p-4" style="background-color: #ef4444; animation: breathing 2s ease-in-out infinite;">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-gray-200">下一个即将上线：</span>
                                <span class="text-xl font-semibold ml-2 text-orange-400">红包 (测试直播间)</span>
                            </div>
                            <div class="text-3xl font-bold font-mono text-red-500" style="background-color: #ef4444; animation: breathing 2s ease-in-out infinite;">
                                00:00:45
                            </div>
                        </div>
                        <div class="mt-2 text-sm text-gray-200">
                            <span class="mr-4">直播间：测试直播间</span>
                            <span>详情：第1/1轮 | 888快币</span>
                        </div>
                    </div>
                    <div class="text-sm text-gray-400">
                        <p>⚠️ 问题：</p>
                        <ul class="list-disc list-inside ml-4 space-y-1">
                            <li>红色背景覆盖了时间文字，看不清楚</li>
                            <li>呼吸灯效果不明显</li>
                            <li>整体视觉效果混乱</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 修复后的效果演示 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-2xl font-semibold mb-4 text-green-400">✅ 修复后的效果</h2>
                <div class="space-y-4">
                    <div class="bg-red-900 border-2 border-red-500 rounded-lg p-4 breathing-container pulse-red">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-gray-200">下一个即将上线：</span>
                                <span class="text-xl font-semibold ml-2 text-orange-400">红包 (测试直播间)</span>
                            </div>
                            <div class="text-3xl font-bold font-mono text-red-500 breathing-text">
                                00:00:45
                            </div>
                        </div>
                        <div class="mt-2 text-sm text-gray-200">
                            <span class="mr-4">直播间：测试直播间</span>
                            <span>详情：第1/1轮 | 888快币</span>
                        </div>
                    </div>
                    <div class="text-sm text-gray-400">
                        <p>✅ 修复：</p>
                        <ul class="list-disc list-inside ml-4 space-y-1">
                            <li>时间文字清晰可见，红色醒目</li>
                            <li>容器有呼吸灯透明度变化</li>
                            <li>外围有脉冲光晕效果</li>
                            <li>整体视觉层次分明</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 不同状态演示 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-6 text-purple-400">不同倒计时状态演示</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 正常状态 -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-yellow-400">正常状态 (>1分钟)</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-400 font-semibold">红包</span>
                        <span class="text-2xl font-bold font-mono text-yellow-400">05:30:15</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-2">无特殊效果，黄色时间显示</div>
                </div>
                
                <!-- 警告状态 -->
                <div class="bg-red-900 border-2 border-red-500 rounded-lg p-4 breathing-container pulse-red">
                    <h3 class="text-lg font-semibold mb-3 text-red-300">警告状态 (≤1分钟)</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-400 font-semibold">红包</span>
                        <span class="text-2xl font-bold font-mono text-red-500 breathing-text">00:00:45</span>
                    </div>
                    <div class="text-xs text-gray-300 mt-2">红色呼吸灯 + 脉冲光晕</div>
                </div>
                
                <!-- 过期状态 -->
                <div class="bg-gray-600 opacity-60 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-gray-300">过期状态</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-400 font-semibold">红包</span>
                        <span class="text-2xl font-bold font-mono text-red-400">已过期</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-2">灰色背景，降低透明度</div>
                </div>
            </div>
        </div>
        
        <!-- 技术说明 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-semibold mb-4 text-cyan-400">技术修复说明</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-blue-400">CSS 动画优化</h3>
                    <div class="bg-gray-900 rounded p-4 text-sm font-mono">
                        <div class="text-green-400">/* 文字呼吸效果 */</div>
                        <div class="text-white">.breathing-text {</div>
                        <div class="text-white ml-4">animation: breathing 2s ease-in-out infinite;</div>
                        <div class="text-white">}</div>
                        <br>
                        <div class="text-green-400">/* 容器脉冲光晕 */</div>
                        <div class="text-white">.pulse-red {</div>
                        <div class="text-white ml-4">animation: pulse-red 2s infinite;</div>
                        <div class="text-white">}</div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-blue-400">JavaScript 逻辑</h3>
                    <div class="bg-gray-900 rounded p-4 text-sm font-mono">
                        <div class="text-green-400">// 分离文字和容器的样式类</div>
                        <div class="text-white">if (timeDiff <= 60000) {</div>
                        <div class="text-white ml-4">element.className = 'text-red-500 breathing-text';</div>
                        <div class="text-white ml-4">container.className = 'bg-red-900 pulse-red';</div>
                        <div class="text-white">}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-8">
            <a href="/" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors mr-4">
                返回主应用
            </a>
            <a href="/test.html" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg transition-colors">
                查看倒计时测试
            </a>
        </div>
    </div>
</body>
</html>
